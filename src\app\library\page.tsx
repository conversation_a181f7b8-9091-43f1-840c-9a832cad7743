'use client'

import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { usePurchases } from '@/hooks/usePurchases'
import { Tutorial } from '@/types'
import { useState, useEffect } from 'react'



function LibraryPage() {
  console.log('🔍 LibraryPage: Component rendering')
  const { user, signOut, loading } = useAuth()
  const { purchasedTutorials, loading: tutorialsLoading } = usePurchases(user?.id)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    console.log('🔍 LibraryPage: Auth loading:', loading, 'Tutorials loading:', tutorialsLoading)
    console.log('🔍 LibraryPage: User:', user)
    console.log('🔍 LibraryPage: Purchased tutorials:', purchasedTutorials)

    // Set loading to false once auth and tutorials are loaded
    if (!loading && !tutorialsLoading) {
      console.log('✅ LibraryPage: All loading complete')
      setIsLoading(false)
    }
  }, [loading, tutorialsLoading, user, purchasedTutorials])

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <p className="text-navy-600 font-body">Loading your library...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Header */}
      <section className="pt-32 pb-16">
        <div className="container-max">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div className="classical-border mb-6 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-display font-semibold mb-4 text-navy-900 text-shadow">
                My Tutorial Library
              </h1>
              <p className="text-lg text-navy-700 font-body">
                Welcome back, {user.name || user.email.split('@')[0]}
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <Link href="/tutorials" className="btn-outline">
                Browse More Tutorials
              </Link>
              <button 
                onClick={handleLogout}
                className="text-navy-600 hover:text-burgundy-700 font-body text-sm transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
            <div className="text-center p-4 bg-cream-100 rounded-lg">
              <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                {purchasedTutorials.length}
              </div>
              <div className="text-navy-600 font-body text-sm">Tutorials Owned</div>
            </div>
            <div className="text-center p-4 bg-cream-100 rounded-lg">
              <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                {purchasedTutorials.reduce((total, tutorial) => total + tutorial.videos.length, 0)}
              </div>
              <div className="text-navy-600 font-body text-sm">Video Lessons</div>
            </div>
            <div className="text-center p-4 bg-cream-100 rounded-lg">
              <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                {Math.floor(purchasedTutorials.reduce((total, tutorial) => 
                  total + tutorial.videos.reduce((videoTotal, video) => videoTotal + video.duration, 0), 0) / 3600)}h
              </div>
              <div className="text-navy-600 font-body text-sm">Total Content</div>
            </div>
            <div className="text-center p-4 bg-cream-100 rounded-lg">
              <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                100%
              </div>
              <div className="text-navy-600 font-body text-sm">Lifetime Access</div>
            </div>
          </div>
        </div>
      </section>

      {/* Tutorial Library */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          {purchasedTutorials.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-20 h-20 bg-navy-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-navy-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-2xl font-display font-medium mb-4 text-navy-800">
                Your Library is Empty
              </h3>
              <p className="text-navy-600 font-body mb-8 max-w-md mx-auto">
                You haven't purchased any tutorials yet. Browse our collection to start your magical education.
              </p>
              <Link href="/tutorials" className="btn-primary">
                Browse Tutorials
              </Link>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {purchasedTutorials.map((tutorial) => (
                <Link 
                  key={tutorial.id} 
                  href={`/watch/${tutorial.id}`}
                  className="group block"
                >
                  <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300 h-full">
                    <div className="flex flex-col h-full">
                      {/* Tutorial Cover */}
                      <div className="aspect-video bg-navy-100 rounded-lg overflow-hidden mb-4 relative">
                        <div className="w-full h-full bg-gradient-to-br from-burgundy-100 to-navy-100 flex items-center justify-center">
                          <div className="text-center">
                            <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-800 transition-colors">
                              <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                            <p className="text-navy-600 font-body text-sm">Watch Now</p>
                          </div>
                        </div>
                        
                        {/* Progress indicator (mock) */}
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-navy-200">
                          <div className="h-full bg-burgundy-600" style={{width: '35%'}}></div>
                        </div>
                      </div>
                      
                      {/* Tutorial Info */}
                      <div className="flex-grow">
                        <h3 className="text-xl md:text-2xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                          {tutorial.title}
                        </h3>
                        
                        <p className="text-navy-700 font-body leading-relaxed mb-4">
                          {tutorial.description.substring(0, 100)}...
                        </p>
                      </div>
                      
                      {/* Tutorial Stats */}
                      <div className="pt-4 border-t border-navy-200">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center text-navy-600 font-body text-sm">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H6a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
                            </svg>
                            {tutorial.videos.length} lessons
                          </div>
                          <div className="flex items-center text-navy-600 font-body text-sm">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {Math.floor(tutorial.videos.reduce((total, video) => total + video.duration, 0) / 60)}min
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                            Continue Learning →
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Continue Learning */}
      <section className="section-padding">
        <div className="container-max">
          <div className="max-w-3xl mx-auto text-center">
            <div className="ornamental-divider mb-8"></div>
            <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 text-navy-900">
              Continue Your Journey
            </h2>
            <p className="text-lg text-navy-700 mb-8 font-body leading-relaxed">
              Expand your magical knowledge with our complete collection of professional tutorials. 
              Each course builds upon the last, creating a comprehensive education in the art of magic.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/tutorials" className="btn-primary">
                Browse All Tutorials
              </Link>
              <Link href="/blog" className="btn-outline">
                Read Magic Insights
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

// Export the component wrapped with authentication protection
export default withAuth(LibraryPage)
